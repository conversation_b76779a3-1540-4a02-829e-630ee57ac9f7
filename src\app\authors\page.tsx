'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  User, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Mail,
  Globe,
  FileText,
  Calendar
} from 'lucide-react'

// 模拟数据
const mockAuthors = [
  {
    id: '1',
    name: '张三',
    bio: '资深技术专家，专注于人工智能和机器学习领域，拥有10年以上的行业经验。',
    email: '<EMAIL>',
    website: 'https://zhangsan.dev',
    avatar: null,
    expertiseAreas: ['人工智能', '机器学习', '深度学习', 'Python'],
    socialLinks: {
      twitter: '@zhangsan',
      linkedin: 'zhangsan',
      github: 'zhangsan'
    },
    postCount: 15,
    isActive: true,
    createdAt: '2024-01-01'
  },
  {
    id: '2',
    name: '李四',
    bio: '前端开发工程师，热爱分享技术知识，专注于React和Vue.js生态系统。',
    email: '<EMAIL>',
    website: 'https://lisi.blog',
    avatar: null,
    expertiseAreas: ['前端开发', 'React', 'Vue.js', 'JavaScript'],
    socialLinks: {
      twitter: '@lisi',
      github: 'lisi'
    },
    postCount: 8,
    isActive: true,
    createdAt: '2024-01-05'
  }
]

export default function AuthorsPage() {
  const [authors, setAuthors] = useState(mockAuthors)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingAuthor, setEditingAuthor] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: '',
    bio: '',
    email: '',
    website: '',
    expertiseAreas: '',
    socialLinks: {
      twitter: '',
      linkedin: '',
      github: ''
    }
  })

  const filteredAuthors = authors.filter(author =>
    author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    author.bio.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreate = () => {
    setShowCreateForm(true)
    setEditingAuthor(null)
    setFormData({
      name: '',
      bio: '',
      email: '',
      website: '',
      expertiseAreas: '',
      socialLinks: {
        twitter: '',
        linkedin: '',
        github: ''
      }
    })
  }

  const handleEdit = (author: any) => {
    setEditingAuthor(author)
    setShowCreateForm(true)
    setFormData({
      name: author.name,
      bio: author.bio,
      email: author.email,
      website: author.website,
      expertiseAreas: author.expertiseAreas.join(', '),
      socialLinks: author.socialLinks
    })
  }

  const handleSave = () => {
    // TODO: 实现保存逻辑
    setShowCreateForm(false)
    setEditingAuthor(null)
  }

  const handleCancel = () => {
    setShowCreateForm(false)
    setEditingAuthor(null)
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">作者管理</h1>
            <p className="text-gray-600">管理博文作者信息和资料</p>
          </div>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="w-4 h-4 mr-2" />
          新建作者
        </Button>
      </div>

      {/* 搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索作者姓名或简介..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingAuthor ? '编辑作者' : '创建新作者'}
            </CardTitle>
            <CardDescription>
              {editingAuthor ? '修改作者信息' : '添加新的博文作者'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  姓名 *
                </label>
                <Input
                  placeholder="输入作者姓名"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱
                </label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                个人简介
              </label>
              <Textarea
                placeholder="简要介绍作者的背景和专业领域"
                value={formData.bio}
                onChange={(e) => setFormData({...formData, bio: e.target.value})}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  个人网站
                </label>
                <Input
                  placeholder="https://example.com"
                  value={formData.website}
                  onChange={(e) => setFormData({...formData, website: e.target.value})}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  专业领域
                </label>
                <Input
                  placeholder="用逗号分隔，如：AI, 机器学习, Python"
                  value={formData.expertiseAreas}
                  onChange={(e) => setFormData({...formData, expertiseAreas: e.target.value})}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                社交媒体链接
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  placeholder="Twitter用户名"
                  value={formData.socialLinks.twitter}
                  onChange={(e) => setFormData({
                    ...formData,
                    socialLinks: {...formData.socialLinks, twitter: e.target.value}
                  })}
                />
                <Input
                  placeholder="LinkedIn用户名"
                  value={formData.socialLinks.linkedin}
                  onChange={(e) => setFormData({
                    ...formData,
                    socialLinks: {...formData.socialLinks, linkedin: e.target.value}
                  })}
                />
                <Input
                  placeholder="GitHub用户名"
                  value={formData.socialLinks.github}
                  onChange={(e) => setFormData({
                    ...formData,
                    socialLinks: {...formData.socialLinks, github: e.target.value}
                  })}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button onClick={handleSave}>
                {editingAuthor ? '更新' : '创建'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 作者列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredAuthors.map((author) => (
          <Card key={author.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-lg">
                      {author.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <CardTitle className="text-lg">{author.name}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <div className="flex items-center space-x-1">
                        <FileText className="w-4 h-4" />
                        <span>{author.postCount} 篇文章</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{author.createdAt}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline" onClick={() => handleEdit(author)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600 text-sm line-clamp-2">
                  {author.bio}
                </p>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  {author.email && (
                    <div className="flex items-center space-x-1">
                      <Mail className="w-4 h-4" />
                      <span>{author.email}</span>
                    </div>
                  )}
                  {author.website && (
                    <div className="flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>个人网站</span>
                    </div>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {author.expertiseAreas.map((area, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded-md"
                    >
                      {area}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center space-x-3 text-sm text-gray-500">
                  {author.socialLinks.twitter && (
                    <span>Twitter: {author.socialLinks.twitter}</span>
                  )}
                  {author.socialLinks.github && (
                    <span>GitHub: {author.socialLinks.github}</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredAuthors.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <User className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到作者</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm ? '尝试调整搜索条件' : '添加您的第一个作者'}
              </p>
              <Button onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                新建作者
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
