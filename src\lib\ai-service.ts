// AI服务集成
// 支持多种AI提供商：OpenAI, Anthropic, Google AI等

export interface AIProvider {
  name: string
  apiKey: string
  baseUrl?: string
}

export interface GeneratePostRequest {
  keywords: string[]
  title?: string
  language: string
  category?: string
  tags?: string[]
  seriesContext?: string
  authorStyle?: string
  promptTemplate?: string
  wordCount?: number
}

export interface GeneratePostResponse {
  title: string
  content: string
  excerpt: string
  metaTitle: string
  metaDescription: string
  metaKeywords: string[]
  seriesSummary?: string
  wordCount: number
}

export interface AIServiceConfig {
  provider: 'openai' | 'anthropic' | 'google' | 'custom'
  apiKey: string
  model: string
  baseUrl?: string
  maxTokens?: number
  temperature?: number
}

export class AIService {
  private config: AIServiceConfig

  constructor(config: AIServiceConfig) {
    this.config = config
  }

  async generatePost(request: GeneratePostRequest): Promise<GeneratePostResponse> {
    try {
      const prompt = this.buildPrompt(request)
      const response = await this.callAI(prompt)
      return this.parseResponse(response, request)
    } catch (error) {
      console.error('AI生成失败:', error)
      throw new Error('AI生成博文失败，请稍后重试')
    }
  }

  async generateTitle(keywords: string[], language: string = 'zh-CN'): Promise<string[]> {
    const prompt = this.buildTitlePrompt(keywords, language)
    const response = await this.callAI(prompt)
    return this.parseTitleResponse(response)
  }

  async generateSEOInfo(content: string, language: string = 'zh-CN'): Promise<{
    metaTitle: string
    metaDescription: string
    metaKeywords: string[]
  }> {
    const prompt = this.buildSEOPrompt(content, language)
    const response = await this.callAI(prompt)
    return this.parseSEOResponse(response)
  }

  async generateSeriesSummary(posts: any[], newPost: any): Promise<string> {
    const prompt = this.buildSeriesSummaryPrompt(posts, newPost)
    const response = await this.callAI(prompt)
    return response.trim()
  }

  private buildPrompt(request: GeneratePostRequest): string {
    const {
      keywords,
      title,
      language,
      category,
      tags,
      seriesContext,
      authorStyle,
      promptTemplate,
      wordCount = 2000
    } = request

    let prompt = promptTemplate || this.getDefaultPrompt(language)

    // 替换变量
    prompt = prompt.replace('{keywords}', keywords.join(', '))
    prompt = prompt.replace('{language}', this.getLanguageName(language))
    prompt = prompt.replace('{title}', title || '自动生成')
    prompt = prompt.replace('{category}', category || '通用')
    prompt = prompt.replace('{tags}', tags?.join(', ') || '')
    prompt = prompt.replace('{word_count}', wordCount.toString())

    // 添加系列上下文
    if (seriesContext) {
      prompt += `\n\n系列背景信息：\n${seriesContext}`
    }

    // 添加作者风格
    if (authorStyle) {
      prompt += `\n\n作者风格要求：\n${authorStyle}`
    }

    return prompt
  }

  private buildTitlePrompt(keywords: string[], language: string): string {
    const languageName = this.getLanguageName(language)
    return `请根据以下关键词生成5个吸引人的${languageName}博文标题：

关键词：${keywords.join(', ')}

要求：
1. 标题要吸引人且具有SEO价值
2. 自然融入关键词
3. 长度适中（10-60字符）
4. 符合${languageName}表达习惯

请直接返回5个标题，每行一个：`
  }

  private buildSEOPrompt(content: string, language: string): string {
    const languageName = this.getLanguageName(language)
    const excerpt = content.substring(0, 500) + '...'
    
    return `请为以下${languageName}文章内容生成SEO信息：

文章内容摘要：
${excerpt}

请生成：
1. SEO标题（50-60字符）
2. Meta描述（150-160字符）
3. 关键词（5-8个）

请按以下JSON格式返回：
{
  "metaTitle": "SEO标题",
  "metaDescription": "Meta描述",
  "metaKeywords": ["关键词1", "关键词2", "关键词3"]
}`
  }

  private buildSeriesSummaryPrompt(posts: any[], newPost: any): string {
    const postsInfo = posts.map(post => `- ${post.title}: ${post.excerpt || post.content.substring(0, 100)}`).join('\n')
    
    return `请为以下博文系列生成一个简洁的总结，用于帮助AI生成后续相关文章：

已有文章：
${postsInfo}

新增文章：
- ${newPost.title}: ${newPost.excerpt || newPost.content.substring(0, 100)}

请生成一个200字以内的系列总结，包含：
1. 系列主题和方向
2. 已覆盖的主要内容点
3. 文章间的关联性
4. 适合后续扩展的方向

总结：`
  }

  private async callAI(prompt: string): Promise<string> {
    switch (this.config.provider) {
      case 'openai':
        return this.callOpenAI(prompt)
      case 'anthropic':
        return this.callAnthropic(prompt)
      case 'google':
        return this.callGoogleAI(prompt)
      default:
        throw new Error(`不支持的AI提供商: ${this.config.provider}`)
    }
  }

  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: this.config.maxTokens || 4000,
        temperature: this.config.temperature || 0.7,
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.statusText}`)
    }

    const data = await response.json()
    return data.choices[0].message.content
  }

  private async callAnthropic(prompt: string): Promise<string> {
    // TODO: 实现Anthropic API调用
    throw new Error('Anthropic API暂未实现')
  }

  private async callGoogleAI(prompt: string): Promise<string> {
    // TODO: 实现Google AI API调用
    throw new Error('Google AI API暂未实现')
  }

  private parseResponse(response: string, request: GeneratePostRequest): GeneratePostResponse {
    try {
      // 尝试解析JSON格式的响应
      const parsed = JSON.parse(response)
      return {
        title: parsed.title || request.title || '未命名文章',
        content: parsed.content || response,
        excerpt: parsed.excerpt || this.generateExcerpt(parsed.content || response),
        metaTitle: parsed.metaTitle || parsed.title || request.title || '未命名文章',
        metaDescription: parsed.metaDescription || this.generateExcerpt(parsed.content || response, 160),
        metaKeywords: parsed.metaKeywords || request.keywords || [],
        seriesSummary: parsed.seriesSummary,
        wordCount: this.countWords(parsed.content || response)
      }
    } catch {
      // 如果不是JSON格式，按纯文本处理
      return {
        title: request.title || '未命名文章',
        content: response,
        excerpt: this.generateExcerpt(response),
        metaTitle: request.title || '未命名文章',
        metaDescription: this.generateExcerpt(response, 160),
        metaKeywords: request.keywords || [],
        wordCount: this.countWords(response)
      }
    }
  }

  private parseTitleResponse(response: string): string[] {
    return response.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .slice(0, 5)
  }

  private parseSEOResponse(response: string): {
    metaTitle: string
    metaDescription: string
    metaKeywords: string[]
  } {
    try {
      return JSON.parse(response)
    } catch {
      return {
        metaTitle: '默认标题',
        metaDescription: '默认描述',
        metaKeywords: []
      }
    }
  }

  private getDefaultPrompt(language: string): string {
    const languageName = this.getLanguageName(language)
    
    return `请根据以下信息生成一篇高质量的${languageName}博文：

关键词：{keywords}
标题：{title}
分类：{category}
标签：{tags}
目标字数：{word_count}字

要求：
1. 文章结构清晰，包含引言、正文和结论
2. 内容专业且易懂，适合目标读者
3. 自然融入关键词，优化SEO
4. 包含实际案例或示例
5. 语言流畅，符合${languageName}表达习惯

请按以下JSON格式返回：
{
  "title": "文章标题",
  "content": "文章正文内容",
  "excerpt": "文章摘要",
  "metaTitle": "SEO标题",
  "metaDescription": "Meta描述",
  "metaKeywords": ["关键词1", "关键词2"]
}`
  }

  private getLanguageName(language: string): string {
    const languageMap: Record<string, string> = {
      'zh-CN': '简体中文',
      'zh-TW': '繁体中文',
      'en-US': '英文',
      'ja-JP': '日文',
      'ko-KR': '韩文'
    }
    return languageMap[language] || '中文'
  }

  private generateExcerpt(content: string, maxLength: number = 200): string {
    const plainText = content.replace(/<[^>]*>/g, '')
    if (plainText.length <= maxLength) {
      return plainText
    }
    return plainText.substring(0, maxLength).trim() + '...'
  }

  private countWords(text: string): number {
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g)?.length || 0
    return chineseChars + englishWords
  }
}

// 默认AI服务实例
export const createAIService = (config?: Partial<AIServiceConfig>): AIService => {
  const defaultConfig: AIServiceConfig = {
    provider: 'openai',
    apiKey: process.env.OPENAI_API_KEY || '',
    model: 'gpt-3.5-turbo',
    maxTokens: 4000,
    temperature: 0.7,
    ...config
  }
  
  return new AIService(defaultConfig)
}
