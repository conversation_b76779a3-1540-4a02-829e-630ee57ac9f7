'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  MessageSquare, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Search,
  Star,
  BarChart3
} from 'lucide-react'

// 模拟数据
const mockPrompts = [
  {
    id: '1',
    name: '技术博文生成模板',
    description: '用于生成技术类博文的标准模板',
    content: '请根据以下关键词生成一篇专业的技术博文：\n\n关键词：{keywords}\n语言：{language}\n\n要求：\n1. 文章结构清晰，包含引言、正文和结论\n2. 内容专业且易懂\n3. 包含实际应用案例\n4. 字数控制在2000-3000字\n5. 优化SEO，自然融入关键词',
    category: '技术',
    language: 'zh-CN',
    usageCount: 45,
    isActive: true,
    createdAt: '2024-01-10'
  },
  {
    id: '2',
    name: '产品评测模板',
    description: '用于生成产品评测类文章的模板',
    content: '请为以下产品撰写一篇详细的评测文章：\n\n产品名称：{product_name}\n关键特性：{features}\n目标用户：{target_audience}\n\n文章结构：\n1. 产品概述\n2. 主要特性分析\n3. 使用体验\n4. 优缺点对比\n5. 购买建议',
    category: '评测',
    language: 'zh-CN',
    usageCount: 23,
    isActive: true,
    createdAt: '2024-01-08'
  }
]

export default function PromptsPage() {
  const [prompts, setPrompts] = useState(mockPrompts)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingPrompt, setEditingPrompt] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    content: '',
    category: '',
    language: 'zh-CN'
  })

  const filteredPrompts = prompts.filter(prompt =>
    prompt.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prompt.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreate = () => {
    setShowCreateForm(true)
    setEditingPrompt(null)
    setFormData({
      name: '',
      description: '',
      content: '',
      category: '',
      language: 'zh-CN'
    })
  }

  const handleEdit = (prompt: any) => {
    setEditingPrompt(prompt)
    setShowCreateForm(true)
    setFormData({
      name: prompt.name,
      description: prompt.description,
      content: prompt.content,
      category: prompt.category,
      language: prompt.language
    })
  }

  const handleSave = () => {
    // TODO: 实现保存逻辑
    setShowCreateForm(false)
    setEditingPrompt(null)
  }

  const handleCancel = () => {
    setShowCreateForm(false)
    setEditingPrompt(null)
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
            <MessageSquare className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Prompt管理</h1>
            <p className="text-gray-600">管理AI生成博文的提示词模板</p>
          </div>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="w-4 h-4 mr-2" />
          新建Prompt
        </Button>
      </div>

      {/* 搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索Prompt名称或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingPrompt ? '编辑Prompt' : '创建新Prompt'}
            </CardTitle>
            <CardDescription>
              {editingPrompt ? '修改现有的Prompt模板' : '创建一个新的Prompt模板'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  名称 *
                </label>
                <Input
                  placeholder="输入Prompt名称"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  分类
                </label>
                <Input
                  placeholder="技术、评测、教程等"
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                描述
              </label>
              <Input
                placeholder="简要描述这个Prompt的用途"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Prompt内容 *
              </label>
              <Textarea
                placeholder="输入Prompt内容，可以使用 {变量名} 来定义变量"
                value={formData.content}
                onChange={(e) => setFormData({...formData, content: e.target.value})}
                rows={8}
              />
              <p className="text-xs text-gray-500 mt-1">
                提示：使用 {'{keywords}'}, {'{language}'}, {'{title}'} 等变量来创建动态内容
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                语言
              </label>
              <select
                value={formData.language}
                onChange={(e) => setFormData({...formData, language: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="zh-CN">简体中文</option>
                <option value="zh-TW">繁体中文</option>
                <option value="en-US">English</option>
                <option value="ja-JP">日本語</option>
                <option value="ko-KR">한국어</option>
              </select>
            </div>

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button onClick={handleSave}>
                {editingPrompt ? '更新' : '创建'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Prompt列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredPrompts.map((prompt) => (
          <Card key={prompt.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{prompt.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {prompt.description}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline" onClick={() => handleEdit(prompt)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600 line-clamp-3">
                    {prompt.content}
                  </p>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md">
                      {prompt.category}
                    </span>
                    <span>{prompt.language}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BarChart3 className="w-4 h-4" />
                    <span>{prompt.usageCount} 次使用</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredPrompts.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到Prompt</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm ? '尝试调整搜索条件' : '创建您的第一个Prompt模板'}
              </p>
              <Button onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                新建Prompt
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
