import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 数据库类型定义
export interface Project {
  id: string
  name: string
  description?: string
  database_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Author {
  id: string
  project_id: string
  name: string
  bio?: string
  avatar_url?: string
  email?: string
  website_url?: string
  social_links?: Record<string, string>
  expertise_areas?: string[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Prompt {
  id: string
  project_id: string
  name: string
  description?: string
  content: string
  variables?: Record<string, any>
  category?: string
  language: string
  is_active: boolean
  usage_count: number
  created_at: string
  updated_at: string
}

export interface Series {
  id: string
  project_id: string
  name: string
  description?: string
  summary?: string
  total_posts: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Post {
  id: string
  project_id: string
  series_id?: string
  author_id?: string
  prompt_id?: string
  title: string
  slug?: string
  content: string
  excerpt?: string
  language: string
  meta_title?: string
  meta_description?: string
  meta_keywords?: string[]
  canonical_url?: string
  category?: string
  tags?: string[]
  keywords_used?: string[]
  generation_prompt?: string
  ai_model?: string
  status: 'draft' | 'published' | 'archived'
  published_at?: string
  series_order?: number
  series_summary?: string
  view_count: number
  word_count?: number
  created_at: string
  updated_at: string
}

export interface PostVersion {
  id: string
  post_id: string
  version_number: number
  title?: string
  content?: string
  changes_summary?: string
  created_at: string
}

export interface SeriesSummary {
  id: string
  series_id: string
  summary_content: string
  post_count: number
  created_at: string
}

// 数据库操作类
export class DatabaseService {
  // 项目相关操作
  static async getProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data as Project[]
  }

  static async createProject(project: Omit<Project, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('projects')
      .insert(project)
      .select()
      .single()
    
    if (error) throw error
    return data as Project
  }

  // 作者相关操作
  static async getAuthors(projectId: string) {
    const { data, error } = await supabase
      .from('authors')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data as Author[]
  }

  static async createAuthor(author: Omit<Author, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('authors')
      .insert(author)
      .select()
      .single()
    
    if (error) throw error
    return data as Author
  }

  // Prompt相关操作
  static async getPrompts(projectId: string) {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data as Prompt[]
  }

  static async createPrompt(prompt: Omit<Prompt, 'id' | 'created_at' | 'updated_at' | 'usage_count'>) {
    const { data, error } = await supabase
      .from('prompts')
      .insert(prompt)
      .select()
      .single()
    
    if (error) throw error
    return data as Prompt
  }

  // 系列相关操作
  static async getSeries(projectId: string) {
    const { data, error } = await supabase
      .from('series')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data as Series[]
  }

  static async createSeries(series: Omit<Series, 'id' | 'created_at' | 'updated_at' | 'total_posts'>) {
    const { data, error } = await supabase
      .from('series')
      .insert(series)
      .select()
      .single()
    
    if (error) throw error
    return data as Series
  }

  // 博文相关操作
  static async getPosts(projectId: string, filters?: {
    status?: string
    seriesId?: string
    authorId?: string
  }) {
    let query = supabase
      .from('posts')
      .select(`
        *,
        series:series_id(name),
        author:author_id(name),
        prompt:prompt_id(name)
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.seriesId) {
      query = query.eq('series_id', filters.seriesId)
    }
    if (filters?.authorId) {
      query = query.eq('author_id', filters.authorId)
    }

    const { data, error } = await query
    
    if (error) throw error
    return data as (Post & {
      series?: { name: string }
      author?: { name: string }
      prompt?: { name: string }
    })[]
  }

  static async createPost(post: Omit<Post, 'id' | 'created_at' | 'updated_at' | 'view_count'>) {
    const { data, error } = await supabase
      .from('posts')
      .insert(post)
      .select()
      .single()
    
    if (error) throw error
    return data as Post
  }

  static async updatePost(id: string, updates: Partial<Post>) {
    const { data, error } = await supabase
      .from('posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data as Post
  }

  static async deletePost(id: string) {
    const { error } = await supabase
      .from('posts')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}
