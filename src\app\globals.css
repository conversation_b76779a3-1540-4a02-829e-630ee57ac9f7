@import "tailwindcss";

:root {
  --background: #fafafa;
  --foreground: #262626;
  --card: #ffffff;
  --card-foreground: #262626;
  --primary: #0095f6;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #262626;
  --muted: #f5f5f5;
  --muted-foreground: #8e8e8e;
  --accent: #0095f6;
  --accent-foreground: #ffffff;
  --border: #dbdbdb;
  --input: #ffffff;
  --ring: #0095f6;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
    --card: #121212;
    --card-foreground: #ffffff;
    --primary: #0095f6;
    --primary-foreground: #ffffff;
    --secondary: #1a1a1a;
    --secondary-foreground: #ffffff;
    --muted: #1a1a1a;
    --muted-foreground: #a8a8a8;
    --accent: #0095f6;
    --accent-foreground: #ffffff;
    --border: #363636;
    --input: #121212;
    --ring: #0095f6;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

/* Instagram风格的渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.instagram-gradient {
  background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
}

/* 毛玻璃效果 */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.card-shadow-lg {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 按钮样式 */
.btn-primary {
  @apply bg-blue-500 text-white font-medium px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 font-medium px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-200 transition-colors duration-200;
}

/* 输入框样式 */
.input-field {
  @apply w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors duration-200 bg-white;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式网格 */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}
