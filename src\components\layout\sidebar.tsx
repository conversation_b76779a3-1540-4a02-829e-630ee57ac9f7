'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  PenTool, 
  FileText, 
  MessageSquare, 
  User, 
  Settings,
  Home,
  Folder,
  BarChart3
} from 'lucide-react'

const navigation = [
  {
    name: '首页',
    href: '/',
    icon: Home,
  },
  {
    name: '博文生成',
    href: '/generate',
    icon: PenTool,
  },
  {
    name: '博文管理',
    href: '/posts',
    icon: FileText,
  },
  {
    name: 'Prompt管理',
    href: '/prompts',
    icon: MessageSquare,
  },
  {
    name: '作者管理',
    href: '/authors',
    icon: User,
  },
  {
    name: '项目管理',
    href: '/projects',
    icon: Folder,
  },
  {
    name: '数据统计',
    href: '/analytics',
    icon: BarChart3,
  },
  {
    name: '设置',
    href: '/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex h-full w-64 flex-col bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <PenTool className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-900">AI博文助手</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors duration-200',
                isActive
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon className={cn(
                'mr-3 h-5 w-5',
                isActive ? 'text-blue-700' : 'text-gray-400'
              )} />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              管理员
            </p>
            <p className="text-xs text-gray-500 truncate">
              <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
