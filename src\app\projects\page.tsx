'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Folder, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Database,
  Upload,
  Download,
  Settings,
  CheckCircle,
  AlertCircle,
  FileText,
  Users
} from 'lucide-react'

// 模拟数据
const mockProjects = [
  {
    id: '1',
    name: '默认项目',
    description: '系统默认项目，用于存储本地生成的博文',
    databaseUrl: 'local',
    isActive: true,
    postCount: 23,
    authorCount: 2,
    status: 'connected',
    createdAt: '2024-01-01',
    lastSync: '2024-01-15 14:30'
  },
  {
    id: '2',
    name: '技术博客项目',
    description: '专门用于技术类博文的项目',
    databaseUrl: '********************************/techblog',
    isActive: true,
    postCount: 45,
    authorCount: 5,
    status: 'connected',
    createdAt: '2024-01-10',
    lastSync: '2024-01-15 12:15'
  },
  {
    id: '3',
    name: '产品评测项目',
    description: '产品评测和推荐类文章项目',
    databaseUrl: '********************************/reviews',
    isActive: false,
    postCount: 12,
    authorCount: 3,
    status: 'disconnected',
    createdAt: '2024-01-05',
    lastSync: '2024-01-10 09:20'
  }
]

const statusConfig = {
  connected: {
    label: '已连接',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  disconnected: {
    label: '未连接',
    color: 'bg-red-100 text-red-800',
    icon: AlertCircle
  },
  connecting: {
    label: '连接中',
    color: 'bg-yellow-100 text-yellow-800',
    icon: AlertCircle
  }
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState(mockProjects)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingProject, setEditingProject] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    databaseUrl: ''
  })

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreate = () => {
    setShowCreateForm(true)
    setEditingProject(null)
    setFormData({
      name: '',
      description: '',
      databaseUrl: ''
    })
  }

  const handleEdit = (project: any) => {
    setEditingProject(project)
    setShowCreateForm(true)
    setFormData({
      name: project.name,
      description: project.description,
      databaseUrl: project.databaseUrl
    })
  }

  const handleSave = () => {
    // TODO: 实现保存逻辑
    setShowCreateForm(false)
    setEditingProject(null)
  }

  const handleCancel = () => {
    setShowCreateForm(false)
    setEditingProject(null)
  }

  const handleSync = (projectId: string) => {
    // TODO: 实现同步逻辑
    console.log('Syncing project:', projectId)
  }

  const handleTestConnection = (projectId: string) => {
    // TODO: 实现连接测试逻辑
    console.log('Testing connection for project:', projectId)
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Folder className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">项目管理</h1>
            <p className="text-gray-600">管理多个项目的数据库连接和数据同步</p>
          </div>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="w-4 h-4 mr-2" />
          新建项目
        </Button>
      </div>

      {/* 搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索项目名称或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingProject ? '编辑项目' : '创建新项目'}
            </CardTitle>
            <CardDescription>
              {editingProject ? '修改项目信息和数据库连接' : '添加新的项目并配置数据库连接'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目名称 *
              </label>
              <Input
                placeholder="输入项目名称"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目描述
              </label>
              <Textarea
                placeholder="简要描述这个项目的用途"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                数据库连接URL
              </label>
              <Input
                placeholder="postgresql://username:password@host:port/database"
                value={formData.databaseUrl}
                onChange={(e) => setFormData({...formData, databaseUrl: e.target.value})}
              />
              <p className="text-xs text-gray-500 mt-1">
                支持PostgreSQL、MySQL等数据库，留空表示使用本地数据库
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button onClick={handleSave}>
                {editingProject ? '更新' : '创建'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 项目列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredProjects.map((project) => {
          const statusInfo = statusConfig[project.status as keyof typeof statusConfig]
          const StatusIcon = statusInfo.icon

          return (
            <Card key={project.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <CardTitle className="text-lg">{project.name}</CardTitle>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full flex items-center space-x-1 ${statusInfo.color}`}>
                        <StatusIcon className="w-3 h-3" />
                        <span>{statusInfo.label}</span>
                      </span>
                    </div>
                    <CardDescription>
                      {project.description}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline" onClick={() => handleEdit(project)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">{project.postCount} 篇博文</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">{project.authorCount} 位作者</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-500">
                    <div className="flex items-center space-x-2 mb-1">
                      <Database className="w-4 h-4" />
                      <span>数据库: {project.databaseUrl === 'local' ? '本地数据库' : '远程数据库'}</span>
                    </div>
                    <div>最后同步: {project.lastSync}</div>
                  </div>

                  <div className="flex items-center space-x-2 pt-2 border-t">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleTestConnection(project.id)}
                    >
                      <Database className="w-4 h-4 mr-1" />
                      测试连接
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleSync(project.id)}
                    >
                      <Upload className="w-4 h-4 mr-1" />
                      同步数据
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="w-4 h-4 mr-1" />
                      导出
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* 空状态 */}
      {filteredProjects.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Folder className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到项目</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm ? '尝试调整搜索条件' : '创建您的第一个项目'}
              </p>
              <Button onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                新建项目
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
