import type { Metadata } from "next";
import "./globals.css";
import { MainLayout } from "@/components/layout/main-layout";

export const metadata: Metadata = {
  title: "AI博文助手 - 智能SEO博文生成系统",
  description: "基于AI的智能博文生成和管理系统，支持多语言、SEO优化、系列管理等功能",
  keywords: ["AI", "博文生成", "SEO", "内容管理", "自动化写作"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="antialiased">
        <MainLayout>
          {children}
        </MainLayout>
      </body>
    </html>
  );
}
