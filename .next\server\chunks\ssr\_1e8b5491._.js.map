{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport { MainLayout } from \"@/components/layout/main-layout\";\n\nexport const metadata: Metadata = {\n  title: \"AI博文助手 - 智能SEO博文生成系统\",\n  description: \"基于AI的智能博文生成和管理系统，支持多语言、SEO优化、系列管理等功能\",\n  keywords: [\"AI\", \"博文生成\", \"SEO\", \"内容管理\", \"自动化写作\"],\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\">\n      <body className=\"antialiased\">\n        <MainLayout>\n          {children}\n        </MainLayout>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAM;QAAQ;QAAO;QAAQ;KAAQ;AAClD;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,8IAAA,CAAA,aAAU;0BACR;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}