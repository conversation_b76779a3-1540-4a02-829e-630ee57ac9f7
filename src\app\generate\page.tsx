'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  PenTool, 
  Settings, 
  Wand2, 
  FileText, 
  Globe, 
  Tag,
  User,
  MessageSquare,
  Folder,
  Sparkles
} from 'lucide-react'

export default function GeneratePage() {
  const [formData, setFormData] = useState({
    keywords: '',
    title: '',
    language: 'zh-CN',
    category: '',
    tags: '',
    seriesId: '',
    authorId: '',
    promptId: '',
    autoTitle: true,
    autoSeo: true,
  })

  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')

  const handleGenerate = async () => {
    if (!formData.keywords.trim()) {
      alert('请输入关键词')
      return
    }

    setIsGenerating(true)
    setGeneratedContent('')

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keywords: formData.keywords.split(',').map(k => k.trim()),
          title: formData.title,
          language: formData.language,
          category: formData.category,
          tags: formData.tags.split(',').map(t => t.trim()).filter(t => t),
          seriesId: formData.seriesId || null,
          authorId: formData.authorId || null,
          promptId: formData.promptId || null,
          autoTitle: formData.autoTitle,
          autoSeo: formData.autoSeo,
        }),
      })

      const result = await response.json()

      if (result.success) {
        setGeneratedContent(result.data.content)
        // 如果自动生成标题且用户没有输入标题，则更新标题
        if (formData.autoTitle && !formData.title) {
          setFormData(prev => ({ ...prev, title: result.data.title }))
        }
      } else {
        alert(result.error || '生成失败，请稍后重试')
      }
    } catch (error) {
      console.error('生成博文失败:', error)
      alert('生成失败，请检查网络连接或稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
          <PenTool className="w-5 h-5 text-white" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI博文生成</h1>
          <p className="text-gray-600">使用AI智能生成高质量SEO博文</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：生成配置 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基础设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>基础设置</span>
              </CardTitle>
              <CardDescription>
                配置博文生成的基本参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  关键词 *
                </label>
                <Input
                  placeholder="输入关键词，用逗号分隔"
                  value={formData.keywords}
                  onChange={(e) => setFormData({...formData, keywords: e.target.value})}
                />
                <p className="text-xs text-gray-500 mt-1">
                  例如：人工智能, 机器学习, 深度学习
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文章标题
                  </label>
                  <Input
                    placeholder="留空自动生成"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    语言
                  </label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={formData.language}
                    onChange={(e) => setFormData({...formData, language: e.target.value})}
                  >
                    <option value="zh-CN">简体中文</option>
                    <option value="zh-TW">繁体中文</option>
                    <option value="en-US">English</option>
                    <option value="ja-JP">日本語</option>
                    <option value="ko-KR">한국어</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    分类
                  </label>
                  <Input
                    placeholder="技术教程"
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    标签
                  </label>
                  <Input
                    placeholder="用逗号分隔"
                    value={formData.tags}
                    onChange={(e) => setFormData({...formData, tags: e.target.value})}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 高级设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5" />
                <span>高级设置</span>
              </CardTitle>
              <CardDescription>
                选择作者、Prompt模板和系列设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="w-4 h-4 inline mr-1" />
                    作者
                  </label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={formData.authorId}
                    onChange={(e) => setFormData({...formData, authorId: e.target.value})}
                  >
                    <option value="">选择作者</option>
                    <option value="default">默认作者</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <MessageSquare className="w-4 h-4 inline mr-1" />
                    Prompt模板
                  </label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={formData.promptId}
                    onChange={(e) => setFormData({...formData, promptId: e.target.value})}
                  >
                    <option value="">选择模板</option>
                    <option value="default">默认模板</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Folder className="w-4 h-4 inline mr-1" />
                    系列
                  </label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={formData.seriesId}
                    onChange={(e) => setFormData({...formData, seriesId: e.target.value})}
                  >
                    <option value="">无系列</option>
                    <option value="ai-series">AI技术系列</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.autoTitle}
                    onChange={(e) => setFormData({...formData, autoTitle: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">自动生成标题</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.autoSeo}
                    onChange={(e) => setFormData({...formData, autoSeo: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">自动生成SEO信息</span>
                </label>
              </div>
            </CardContent>
          </Card>

          {/* 生成按钮 */}
          <div className="flex justify-center">
            <Button
              onClick={handleGenerate}
              disabled={!formData.keywords || isGenerating}
              className="px-8 py-3 text-lg"
            >
              {isGenerating ? (
                <>
                  <Wand2 className="w-5 h-5 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5 mr-2" />
                  生成博文
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 右侧：预览和快捷操作 */}
        <div className="space-y-6">
          {/* 生成预览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>生成预览</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {generatedContent ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">{generatedContent}</p>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <FileText className="w-4 h-4 mr-1" />
                      编辑
                    </Button>
                    <Button size="sm" variant="outline">
                      保存草稿
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>生成的内容将在这里显示</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 快捷操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快捷操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <User className="w-4 h-4 mr-2" />
                管理作者
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MessageSquare className="w-4 h-4 mr-2" />
                管理Prompt
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Folder className="w-4 h-4 mr-2" />
                管理系列
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
