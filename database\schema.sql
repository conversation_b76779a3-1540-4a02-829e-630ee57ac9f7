-- AI博文生成系统数据库设计
-- 创建时间: 2025-08-04

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. 项目表 (projects)
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    database_url TEXT, -- 用于连接其他项目数据库
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 作者信息表 (authors)
CREATE TABLE authors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    bio TEXT,
    avatar_url TEXT,
    email VARCHAR(255),
    website_url TEXT,
    social_links JSONB, -- 存储社交媒体链接
    expertise_areas TEXT[], -- 专业领域数组
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Prompt模板表 (prompts)
CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    variables JSONB, -- 存储prompt中的变量定义
    category VARCHAR(100), -- prompt分类
    language VARCHAR(10) DEFAULT 'zh-CN',
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 博文系列表 (series)
CREATE TABLE series (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    summary TEXT, -- 系列总结，用于AI生成关联内容
    total_posts INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 博文表 (posts)
CREATE TABLE posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    series_id UUID REFERENCES series(id) ON DELETE SET NULL,
    author_id UUID REFERENCES authors(id) ON DELETE SET NULL,
    prompt_id UUID REFERENCES prompts(id) ON DELETE SET NULL,
    
    -- 基本信息
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,
    language VARCHAR(10) DEFAULT 'zh-CN',
    
    -- SEO信息
    meta_title VARCHAR(500),
    meta_description TEXT,
    meta_keywords TEXT[],
    canonical_url TEXT,
    
    -- 分类和标签
    category VARCHAR(100),
    tags TEXT[],
    
    -- 生成信息
    keywords_used TEXT[], -- 用于生成的关键词
    generation_prompt TEXT, -- 实际使用的prompt
    ai_model VARCHAR(100), -- 使用的AI模型
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft', -- draft, published, archived
    published_at TIMESTAMP WITH TIME ZONE,
    
    -- 系列相关
    series_order INTEGER, -- 在系列中的顺序
    series_summary TEXT, -- 本文对系列的贡献总结
    
    -- 统计信息
    view_count INTEGER DEFAULT 0,
    word_count INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 博文版本历史表 (post_versions)
CREATE TABLE post_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title VARCHAR(500),
    content TEXT,
    changes_summary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 系列历史总结表 (series_summaries)
CREATE TABLE series_summaries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    summary_content TEXT NOT NULL,
    post_count INTEGER NOT NULL, -- 当时系列包含的文章数量
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_posts_project_id ON posts(project_id);
CREATE INDEX idx_posts_series_id ON posts(series_id);
CREATE INDEX idx_posts_author_id ON posts(author_id);
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_published_at ON posts(published_at);
CREATE INDEX idx_posts_language ON posts(language);
CREATE INDEX idx_posts_tags ON posts USING GIN(tags);
CREATE INDEX idx_posts_keywords ON posts USING GIN(keywords_used);

CREATE INDEX idx_authors_project_id ON authors(project_id);
CREATE INDEX idx_prompts_project_id ON prompts(project_id);
CREATE INDEX idx_series_project_id ON series(project_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_authors_updated_at BEFORE UPDATE ON authors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON prompts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_series_updated_at BEFORE UPDATE ON series FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建系列文章数量更新触发器
CREATE OR REPLACE FUNCTION update_series_post_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE series SET total_posts = total_posts + 1 WHERE id = NEW.series_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE series SET total_posts = total_posts - 1 WHERE id = OLD.series_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.series_id IS DISTINCT FROM NEW.series_id THEN
            IF OLD.series_id IS NOT NULL THEN
                UPDATE series SET total_posts = total_posts - 1 WHERE id = OLD.series_id;
            END IF;
            IF NEW.series_id IS NOT NULL THEN
                UPDATE series SET total_posts = total_posts + 1 WHERE id = NEW.series_id;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_series_post_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON posts
    FOR EACH ROW EXECUTE FUNCTION update_series_post_count();

-- 插入默认项目
INSERT INTO projects (name, description, is_active) 
VALUES ('默认项目', '系统默认项目，用于存储本地生成的博文', true);
