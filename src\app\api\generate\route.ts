import { NextRequest, NextResponse } from 'next/server'
import { createAIService, GeneratePostRequest } from '@/lib/ai-service'
import { DatabaseService } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      keywords,
      title,
      language = 'zh-CN',
      category,
      tags,
      seriesId,
      authorId,
      promptId,
      autoTitle,
      autoSeo,
      projectId
    } = body

    // 验证必需参数
    if (!keywords || keywords.length === 0) {
      return NextResponse.json(
        { error: '关键词不能为空' },
        { status: 400 }
      )
    }

    // 获取系列上下文（如果指定了系列）
    let seriesContext = ''
    if (seriesId) {
      try {
        // TODO: 获取系列历史文章信息
        seriesContext = '这是一个技术系列文章...' // 临时占位符
      } catch (error) {
        console.warn('获取系列上下文失败:', error)
      }
    }

    // 获取作者风格（如果指定了作者）
    let authorStyle = ''
    if (authorId) {
      try {
        // TODO: 获取作者写作风格信息
        authorStyle = '专业技术写作风格...' // 临时占位符
      } catch (error) {
        console.warn('获取作者风格失败:', error)
      }
    }

    // 获取Prompt模板（如果指定了）
    let promptTemplate = ''
    if (promptId) {
      try {
        // TODO: 从数据库获取Prompt模板
        promptTemplate = '请根据关键词生成博文...' // 临时占位符
      } catch (error) {
        console.warn('获取Prompt模板失败:', error)
      }
    }

    // 创建AI服务实例
    const aiService = createAIService({
      provider: 'openai',
      apiKey: process.env.OPENAI_API_KEY,
      model: 'gpt-3.5-turbo'
    })

    // 构建生成请求
    const generateRequest: GeneratePostRequest = {
      keywords: Array.isArray(keywords) ? keywords : keywords.split(',').map((k: string) => k.trim()),
      title: autoTitle ? undefined : title,
      language,
      category,
      tags: Array.isArray(tags) ? tags : (tags ? tags.split(',').map((t: string) => t.trim()) : []),
      seriesContext,
      authorStyle,
      promptTemplate,
      wordCount: 2000
    }

    // 生成博文
    const result = await aiService.generatePost(generateRequest)

    // 如果需要自动生成SEO信息
    if (autoSeo && !promptTemplate) {
      try {
        const seoInfo = await aiService.generateSEOInfo(result.content, language)
        result.metaTitle = seoInfo.metaTitle
        result.metaDescription = seoInfo.metaDescription
        result.metaKeywords = seoInfo.metaKeywords
      } catch (error) {
        console.warn('生成SEO信息失败:', error)
      }
    }

    // 生成系列总结（如果是系列文章）
    if (seriesId) {
      try {
        // TODO: 获取系列中的其他文章
        const seriesPosts: any[] = []
        const seriesSummary = await aiService.generateSeriesSummary(seriesPosts, result)
        result.seriesSummary = seriesSummary
      } catch (error) {
        console.warn('生成系列总结失败:', error)
      }
    }

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('博文生成失败:', error)
    
    // 根据错误类型返回不同的错误信息
    if (error instanceof Error) {
      if (error.message.includes('API')) {
        return NextResponse.json(
          { error: 'AI服务暂时不可用，请稍后重试' },
          { status: 503 }
        )
      }
      
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: '生成博文时发生未知错误' },
      { status: 500 }
    )
  }
}

// 生成标题建议的API
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const keywords = searchParams.get('keywords')
    const language = searchParams.get('language') || 'zh-CN'

    if (!keywords) {
      return NextResponse.json(
        { error: '关键词不能为空' },
        { status: 400 }
      )
    }

    const aiService = createAIService()
    const keywordArray = keywords.split(',').map(k => k.trim())
    const titles = await aiService.generateTitle(keywordArray, language)

    return NextResponse.json({
      success: true,
      data: { titles }
    })

  } catch (error) {
    console.error('生成标题失败:', error)
    return NextResponse.json(
      { error: '生成标题失败，请稍后重试' },
      { status: 500 }
    )
  }
}
