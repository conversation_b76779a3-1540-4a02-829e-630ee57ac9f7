# AI博文助手 - 智能SEO博文生成系统

一个基于AI的智能博文生成和管理系统，支持多语言、SEO优化、系列管理等功能。采用现代化的Instagram风格界面设计，为内容创作者提供高效的写作工具。

## ✨ 主要功能

### 📝 博文生成
- **智能生成**：基于关键词自动生成高质量博文
- **多语言支持**：支持中文、英文、日文、韩文等多种语言
- **SEO优化**：自动生成SEO标题、描述和关键词
- **系列管理**：支持创建关联性博文系列
- **自定义模板**：可使用自定义Prompt模板

### 📚 内容管理
- **博文管理**：预览、编辑、发布、删除博文
- **作者管理**：管理作者信息和写作风格
- **Prompt管理**：创建和管理AI生成模板
- **项目管理**：支持多项目数据库连接

### 🔧 技术特性
- **现代化界面**：Instagram风格的美观UI设计
- **响应式设计**：完美适配桌面和移动设备
- **数据库支持**：基于Supabase的云数据库
- **AI集成**：支持OpenAI、Anthropic等多种AI服务

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd auto-blog
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
复制 `.env.local.example` 为 `.env.local` 并填入配置：
```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件：
```env
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# AI API配置
OPENAI_API_KEY=your_openai_api_key

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **设置数据库**
在Supabase中执行 `database/schema.sql` 文件来创建数据表。

5. **启动开发服务器**
```bash
npm run dev
```

6. **访问应用**
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📖 使用指南

### 生成博文
1. 进入"博文生成"页面
2. 输入关键词（必填）
3. 选择语言、分类等设置
4. 可选择作者、Prompt模板、系列等高级选项
5. 点击"生成博文"按钮
6. 等待AI生成完成，可预览和编辑结果

### 管理内容
- **博文管理**：查看所有博文，支持搜索、筛选、编辑
- **作者管理**：添加和管理作者信息
- **Prompt管理**：创建自定义生成模板
- **项目管理**：连接多个数据库项目

## 🏗️ 技术架构

### 前端技术栈
- **Next.js 15**：React全栈框架
- **TypeScript**：类型安全的JavaScript
- **Tailwind CSS**：实用优先的CSS框架
- **Lucide React**：现代化图标库

### 后端技术栈
- **Supabase**：开源的Firebase替代方案
- **PostgreSQL**：关系型数据库
- **Next.js API Routes**：服务端API

### AI集成
- **OpenAI GPT**：主要的AI文本生成服务
- **多提供商支持**：可扩展支持其他AI服务

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router页面
│   ├── api/               # API路由
│   ├── generate/          # 博文生成页面
│   ├── posts/             # 博文管理页面
│   ├── prompts/           # Prompt管理页面
│   ├── authors/           # 作者管理页面
│   └── projects/          # 项目管理页面
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   └── layout/           # 布局组件
└── lib/                  # 工具库和服务
    ├── supabase.ts       # 数据库服务
    ├── ai-service.ts     # AI集成服务
    └── utils.ts          # 工具函数
```

## 🔧 配置说明

### AI服务配置
系统支持多种AI服务提供商：

- **OpenAI**：需要API密钥
- **Anthropic**：需要API密钥（待实现）
- **Google AI**：需要API密钥（待实现）

### 数据库配置
使用Supabase作为主要数据库，支持：
- 用户认证
- 实时数据同步
- 文件存储
- 边缘函数

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用 MIT 许可证。
