{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/app/page.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { PenTool, FileText, MessageSquare, User, TrendingUp, Clock } from \"lucide-react\"\nimport Link from \"next/link\"\n\nexport default function Home() {\n  return (\n    <div className=\"space-y-8\">\n      {/* 欢迎区域 */}\n      <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 text-white\">\n        <h1 className=\"text-3xl font-bold mb-2\">欢迎使用 AI博文助手</h1>\n        <p className=\"text-blue-100 mb-6\">\n          智能SEO博文生成系统，让内容创作变得简单高效\n        </p>\n        <Link href=\"/generate\">\n          <Button className=\"bg-white text-blue-600 hover:bg-blue-50\">\n            <PenTool className=\"w-4 h-4 mr-2\" />\n            开始创作\n          </Button>\n        </Link>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">总博文数</CardTitle>\n            <FileText className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">0</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +0% 较上月\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">已发布</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">0</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +0% 较上月\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Prompt模板</CardTitle>\n            <MessageSquare className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">0</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +0 个新增\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">作者数量</CardTitle>\n            <User className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">0</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +0 个新增\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* 快速操作 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>快速操作</CardTitle>\n            <CardDescription>\n              常用功能快速入口\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <Link href=\"/generate\" className=\"block\">\n              <Button variant=\"outline\" className=\"w-full justify-start\">\n                <PenTool className=\"w-4 h-4 mr-2\" />\n                生成新博文\n              </Button>\n            </Link>\n            <Link href=\"/prompts\" className=\"block\">\n              <Button variant=\"outline\" className=\"w-full justify-start\">\n                <MessageSquare className=\"w-4 h-4 mr-2\" />\n                管理Prompt模板\n              </Button>\n            </Link>\n            <Link href=\"/authors\" className=\"block\">\n              <Button variant=\"outline\" className=\"w-full justify-start\">\n                <User className=\"w-4 h-4 mr-2\" />\n                管理作者信息\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>最近活动</CardTitle>\n            <CardDescription>\n              系统最新动态\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm text-muted-foreground\">\n                    系统初始化完成\n                  </p>\n                  <p className=\"text-xs text-muted-foreground flex items-center\">\n                    <Clock className=\"w-3 h-3 mr-1\" />\n                    刚刚\n                  </p>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAW,WAAU;;kDACpB,8OAAC;wCAAU,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC;;0CACC,8OAAC;gCAAW,WAAU;;kDACpB,8OAAC;wCAAU,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC;;0CACC,8OAAC;gCAAW,WAAU;;kDACpB,8OAAC;wCAAU,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC;;0CACC,8OAAC;gCAAW,WAAU;;kDACpB,8OAAC;wCAAU,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;0CAElB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;;kDACC,8OAAC;kDAAU;;;;;;kDACX,8OAAC;kDAAgB;;;;;;;;;;;;0CAInB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAC/B,cAAA,8OAAC;4CAAO,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIxC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC9B,cAAA,8OAAC;4CAAO,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI9C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC9B,cAAA,8OAAC;4CAAO,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAOzC,8OAAC;;0CACC,8OAAC;;kDACC,8OAAC;kDAAU;;;;;;kDACX,8OAAC;kDAAgB;;;;;;;;;;;;0CAInB,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAG7C,8OAAC;wDAAE,WAAU;;0EACX,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD", "debugId": null}}]}