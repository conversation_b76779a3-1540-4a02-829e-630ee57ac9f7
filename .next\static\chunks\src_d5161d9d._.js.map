{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// 生成URL友好的slug\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // 移除特殊字符\n    .replace(/[\\s_-]+/g, '-') // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符\n}\n\n// 计算文本字数\nexport function countWords(text: string): number {\n  // 中文字符计数\n  const chineseChars = (text.match(/[\\u4e00-\\u9fff]/g) || []).length\n  // 英文单词计数\n  const englishWords = text.replace(/[\\u4e00-\\u9fff]/g, '').match(/\\b\\w+\\b/g)?.length || 0\n  \n  return chineseChars + englishWords\n}\n\n// 生成摘要\nexport function generateExcerpt(content: string, maxLength: number = 200): string {\n  const plainText = content.replace(/<[^>]*>/g, '') // 移除HTML标签\n  if (plainText.length <= maxLength) {\n    return plainText\n  }\n  return plainText.substring(0, maxLength).trim() + '...'\n}\n\n// 格式化日期\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const target = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60)\n    return `${minutes}分钟前`\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600)\n    return `${hours}小时前`\n  } else if (diffInSeconds < 2592000) {\n    const days = Math.floor(diffInSeconds / 86400)\n    return `${days}天前`\n  } else {\n    return formatDate(date)\n  }\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证URL格式\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch {\n    return false\n  }\n}\n\n// 深拷贝对象\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n  \n  if (obj instanceof Date) {\n    return new Date(obj.getTime()) as T\n  }\n  \n  if (obj instanceof Array) {\n    return obj.map(item => deepClone(item)) as T\n  }\n  \n  if (typeof obj === 'object') {\n    const clonedObj = {} as T\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n  \n  return obj\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean = false\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => {\n        inThrottle = false\n      }, limit)\n    }\n  }\n}\n\n// 生成随机ID\nexport function generateId(length: number = 8): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n  let result = ''\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  return result\n}\n\n// 文件大小格式化\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 颜色工具\nexport const colors = {\n  primary: {\n    50: '#fef7ee',\n    100: '#fdedd3',\n    500: '#f97316',\n    600: '#ea580c',\n    700: '#c2410c',\n  },\n  gray: {\n    50: '#f9fafb',\n    100: '#f3f4f6',\n    200: '#e5e7eb',\n    300: '#d1d5db',\n    400: '#9ca3af',\n    500: '#6b7280',\n    600: '#4b5563',\n    700: '#374151',\n    800: '#1f2937',\n    900: '#111827',\n  }\n}\n\n// 状态颜色映射\nexport const statusColors = {\n  draft: 'bg-gray-100 text-gray-800',\n  published: 'bg-green-100 text-green-800',\n  archived: 'bg-red-100 text-red-800',\n}\n\n// 语言选项\nexport const languageOptions = [\n  { value: 'zh-CN', label: '简体中文' },\n  { value: 'zh-TW', label: '繁体中文' },\n  { value: 'en-US', label: 'English' },\n  { value: 'ja-JP', label: '日本語' },\n  { value: 'ko-KR', label: '한국어' },\n  { value: 'es-ES', label: 'Español' },\n  { value: 'fr-FR', label: 'Français' },\n  { value: 'de-DE', label: 'Deutsch' },\n]\n\n// 博文类别选项\nexport const categoryOptions = [\n  '技术教程',\n  '行业分析',\n  '产品评测',\n  '新闻资讯',\n  '经验分享',\n  '工具推荐',\n  '趋势预测',\n  '案例研究',\n  '其他'\n]\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,YAAY,KAAK,eAAe;KACxC,OAAO,CAAC,YAAY,IAAI,cAAc;;AAC3C;AAGO,SAAS,WAAW,IAAY;QAIhB;IAHrB,SAAS;IACT,MAAM,eAAe,CAAC,KAAK,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM;IAClE,SAAS;IACT,MAAM,eAAe,EAAA,sBAAA,KAAK,OAAO,CAAC,oBAAoB,IAAI,KAAK,CAAC,yBAA3C,0CAAA,oBAAwD,MAAM,KAAI;IAEvF,OAAO,eAAe;AACxB;AAGO,SAAS,gBAAgB,OAAe;QAAE,YAAA,iEAAoB;IACnE,MAAM,YAAY,QAAQ,OAAO,CAAC,YAAY,IAAI,WAAW;;IAC7D,IAAI,UAAU,MAAM,IAAI,WAAW;QACjC,OAAO;IACT;IACA,OAAO,UAAU,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AACpD;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,KAAK;IACxB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,OAAO,OAAO,EAAE,IAAI;IAEtE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,AAAC,GAAU,OAAR,SAAQ;IACpB,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,AAAC,GAAQ,OAAN,OAAM;IAClB,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,AAAC,GAAO,OAAL,MAAK;IACjB,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QAC3C,OAAO;IACT;IAEA,IAAI,eAAe,MAAM;QACvB,OAAO,IAAI,KAAK,IAAI,OAAO;IAC7B;IAEA,IAAI,eAAe,OAAO;QACxB,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IACnC;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO;yCAAI;YAAA;;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI,aAAsB;IAE1B,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;AACF;AAGO,SAAS;QAAW,SAAA,iEAAiB;IAC1C,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,MAAM,SAAS;IACpB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,eAAe;IAC1B,OAAO;IACP,WAAW;IACX,UAAU;AACZ;AAGO,MAAM,kBAAkB;IAC7B;QAAE,OAAO;QAAS,OAAO;IAAO;IAChC;QAAE,OAAO;QAAS,OAAO;IAAO;IAChC;QAAE,OAAO;QAAS,OAAO;IAAU;IACnC;QAAE,OAAO;QAAS,OAAO;IAAM;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAM;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAU;IACnC;QAAE,OAAO;QAAS,OAAO;IAAW;IACpC;QAAE,OAAO;QAAS,OAAO;IAAU;CACpC;AAGM,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { \n  PenTool, \n  FileText, \n  MessageSquare, \n  User, \n  Settings,\n  Home,\n  Folder,\n  BarChart3\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: '首页',\n    href: '/',\n    icon: Home,\n  },\n  {\n    name: '博文生成',\n    href: '/generate',\n    icon: PenTool,\n  },\n  {\n    name: '博文管理',\n    href: '/posts',\n    icon: FileText,\n  },\n  {\n    name: 'Prompt管理',\n    href: '/prompts',\n    icon: MessageSquare,\n  },\n  {\n    name: '作者管理',\n    href: '/authors',\n    icon: User,\n  },\n  {\n    name: '项目管理',\n    href: '/projects',\n    icon: Folder,\n  },\n  {\n    name: '数据统计',\n    href: '/analytics',\n    icon: BarChart3,\n  },\n  {\n    name: '设置',\n    href: '/settings',\n    icon: Settings,\n  },\n]\n\nexport function Sidebar() {\n  const pathname = usePathname()\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n            <PenTool className=\"w-5 h-5 text-white\" />\n          </div>\n          <span className=\"text-xl font-bold text-gray-900\">AI博文助手</span>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors duration-200',\n                isActive\n                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n              )}\n            >\n              <item.icon className={cn(\n                'mr-3 h-5 w-5',\n                isActive ? 'text-blue-700' : 'text-gray-400'\n              )} />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\">\n            <User className=\"w-4 h-4 text-gray-600\" />\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-medium text-gray-900 truncate\">\n              管理员\n            </p>\n            <p className=\"text-xs text-gray-500 truncate\">\n              <EMAIL>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAgBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,+MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6LAAC;4BAAK,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAKtD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+FACA,WACI,wDACA;;0CAGN,6LAAC,KAAK,IAAI;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,gBACA,WAAW,kBAAkB;;;;;;4BAE9B,KAAK,IAAI;;uBAbL,KAAK,IAAI;;;;;gBAgBpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GA1DgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uOACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-lg border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Menu } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  return (\n    <header className=\"h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6\">\n      {/* Left side */}\n      <div className=\"flex items-center space-x-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={onMenuClick}\n          className=\"lg:hidden\"\n        >\n          <Menu className=\"h-5 w-5\" />\n        </Button>\n        \n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <Input\n            placeholder=\"搜索博文、作者、标签...\"\n            className=\"pl-10 w-80 bg-gray-50 border-0 focus:bg-white focus:ring-1 focus:ring-blue-500\"\n          />\n        </div>\n      </div>\n\n      {/* Right side */}\n      <div className=\"flex items-center space-x-4\">\n        <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            3\n          </span>\n        </Button>\n        \n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n            <span className=\"text-white text-sm font-medium\">A</span>\n          </div>\n          <div className=\"hidden md:block\">\n            <p className=\"text-sm font-medium text-gray-900\">管理员</p>\n            <p className=\"text-xs text-gray-500\">在线</p>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAUO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;IACrB,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,WAAU;;0CAC5C,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAA+G;;;;;;;;;;;;kCAKjI,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;KA5CgB", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/auto-blog/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { cn } from '@/lib/utils'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"h-screen flex bg-gray-50\">\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <Sidebar />\n      </div>\n\n      {/* Overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Header onMenuClick={() => setSidebarOpen(!sidebarOpen)} />\n        \n        <main className=\"flex-1 overflow-auto\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,WAAW,KAA6B;QAA7B,EAAE,QAAQ,EAAmB,GAA7B;;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kIACA,cAAc,kBAAkB;0BAEhC,cAAA,6LAAC,0IAAA,CAAA,UAAO;;;;;;;;;;YAIT,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,aAAa,IAAM,eAAe,CAAC;;;;;;kCAE3C,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAjCgB;KAAA", "debugId": null}}]}